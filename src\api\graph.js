import request from './axios';

/**
 * 获取知识图谱列表
 * @param {Object} params 查询参数
 * @param {Object} params.zstpGraph ZstpGraph对象，包含查询条件
 * @param {string} params.zstpGraph.name 图谱名字
 * @param {string} params.zstpGraph.content 图谱内容文本
 * @param {number} params.zstpGraph.courseId 课程id
 * @param {string} params.zstpGraph.graphType 图谱类型，0为图谱,1为章节
 * @param {number} params.pageNum 页码，默认1
 * @param {number} params.pageSize 每页大小，默认100
 * @returns {Promise<Object>} 图谱列表响应数据，格式为TableDataInfo
 */
export const getGraphList = (params = {}) => {
  console.log('正在获取知识图谱列表...', params);

  // 构建查询参数
  const queryParams = {};

  // 处理zstpGraph对象参数
  if (params.zstpGraph && typeof params.zstpGraph === 'object') {
    // 过滤掉空值和undefined的属性
    const cleanZstpGraph = {};
    Object.keys(params.zstpGraph).forEach(key => {
      const value = params.zstpGraph[key];
      if (value !== null && value !== undefined && value !== '') {
        cleanZstpGraph[key] = value;
      }
    });

    // 如果有有效的查询条件，添加到参数中
    if (Object.keys(cleanZstpGraph).length > 0) {
      queryParams.zstpGraph = cleanZstpGraph;
    }
  }

  // 添加分页参数
  queryParams.pageNum = params.pageNum || 1;
  queryParams.pageSize = params.pageSize || 100;

  return request({
    url: '/core/zstp/list',
    method: 'get',
    params: queryParams,
    // 确保使用正确的Content-Type
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取知识图谱列表成功:', response);

    // 处理标准的TableDataInfo响应格式
    if (response && typeof response === 'object') {
      // 检查是否为标准的TableDataInfo格式
      if (response.hasOwnProperty('total') && response.hasOwnProperty('rows') && response.hasOwnProperty('code')) {
        console.log('返回标准TableDataInfo格式');
        return response;
      }
      // 兼容旧格式：只有rows数组的情况
      else if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准响应格式，补充TableDataInfo结构');
        return {
          total: response.rows.length,
          rows: response.rows,
          code: 200,
          msg: 'success'
        };
      }
      // 兼容旧格式：直接返回数组的情况
      else if (Array.isArray(response)) {
        console.log('返回数组格式，转换为TableDataInfo结构');
        return {
          total: response.length,
          rows: response,
          code: 200,
          msg: 'success'
        };
      }
      // 兼容旧格式：嵌套data的情况
      else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取知识图谱列表数据，转换为TableDataInfo结构');
        return {
          total: response.data.length,
          rows: response.data,
          code: 200,
          msg: 'success'
        };
      }
    }

    console.warn('响应格式不符合预期，返回空的TableDataInfo结构');
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: '响应格式不符合预期'
    };
  }).catch(error => {
    console.error('获取知识图谱列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取知识图谱列表失败'
    };
  });
};

/**
 * 获取指定ID的知识图谱
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 图谱数据
 */
export const getGraph = (graphId) => {
  return request({
    url: `/core/zstp/${graphId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 创建新的知识图谱
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 创建结果
 */
export const createGraph = (graphData) => {
  console.log('创建新知识图谱:', graphData);
  return request({
    url: '/core/zstp',
    method: 'post',
    data: graphData
  }).then(response => {
    console.log('创建知识图谱成功:', response);
    return response;
  }).catch(error => {
    console.error('创建知识图谱失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新知识图谱
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateGraph = (graphData) => {
  console.log('更新知识图谱信息:', graphData);
  return request({
    url: `/core/zstp`,
    method: 'put',
    data: graphData
  }).then(response => {
    console.log('更新知识图谱成功:', response);
    return response;
  }).catch(error => {
    console.error('更新知识图谱失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除知识图谱
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteGraph = (graphId) => {
  console.log(`删除知识图谱: ${graphId}`);
  return request({
    url: `/core/zstp/${graphId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除知识图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除知识图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取知识图谱大纲
 * @param {string} courseId 知识图谱ID
 * @returns {Promise<Object>} 知识图谱大纲数据
 */
export const getCourseOutline = (courseId) => {
  console.log(`正在获取知识图谱大纲: ${courseId}`);
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'get'
  }).then(response => {
    console.log(`获取知识图谱${courseId}大纲成功:`, response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.success && response.data) {
        console.log('从嵌套响应中提取大纲数据');
        return response.data;
      } else if (response.nodes || response.title) {
        return response;
      }
    }
    console.warn('响应格式不符合预期');
    return null;
  }).catch(error => {
    console.error(`获取知识图谱 ${courseId} 大纲失败:`, error);
    throw error;
  });
};

/**
 * 获取知识图谱节点列表
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 节点列表
 */
export const getNodeList = (graphId) => {
  console.log(`正在获取节点数据: ${graphId}`);
  return request({
    url: `core/node/list`,
    method: 'get',
    params: {
      graphId: graphId
    }
  }).then(response => {
    console.log(`获取节点数据:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点列表失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点样式
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 节点样式
 */
export const getNodeStyle = (nodeId) => {
  return request({
    url: `core/style/${nodeId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取节点样式:`, response);
    return response.data;
  }).catch(error => {
    console.error(`获取节点样式失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点连线
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 连线列表
 */
export const getNodeLines = (nodeId) => {
  return request({
    url: `core/line/list`,
    method: 'get',
    params: {
      nodeId: nodeId
    }
  }).then(response => {
    console.log(`获取节点线条:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点线条失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 创建节点
 * @param {Object} node 节点数据
 * @returns {Promise<Object>} 创建结果
 */
export const createNode = (node) => {
  return request({
    url: `core/node`,
    method: 'post',
    data: node
  }).then(response => {
    console.log(`创建节点:`, response);
    return response.data;
  }).catch(error => {
    console.error('创建节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新节点
 * @param {Object} node 节点数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateNode = (node) => {
  return request({
    url: `core/node`,
    method: 'put',
    data: node
  }).then(response => {
    console.log(`更新节点:`, response);
    return response.data;
  }).catch(error => {
    console.error('更新节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除节点
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteNode = (nodeId) => {
  return request({
    url: `/core/node/${nodeId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除节点:`, response);
    return response.data;
  }).catch(error => {
    console.error(`删除节点 ${nodeId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点数据
 * @param {Object} nodeData 节点数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNode = (nodeData) => {
  return request({
    url: '/core/node',
    method: 'post',
    data: nodeData
  }).then(response => {
    console.log('保存节点成功:', response);
    return response;
  }).catch(error => {
    console.error('保存节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 保存连线数据
 * @param {Object} lineData 连线数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveLine = (lineData) => {
  return request({
    url: '/core/line',
    method: 'post',
    data: lineData
  }).then(response => {
    console.log('保存连线成功:', response);
    return response;
  }).catch(error => {
    console.error('保存连线失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除连线
 * @param {string} lineId 连线ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteLine = (lineId) => {
  return request({
    url: `/core/line/${lineId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除连线 ${lineId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除连线 ${lineId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点样式
 * @param {Object} styleData 样式数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNodeStyle = (styleData) => {
  return request({
    url: '/core/style',
    method: 'post',
    data: styleData
  }).then(response => {
    console.log('保存样式成功:', response);
    return response;
  }).catch(error => {
    console.error('保存样式失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 保存知识图谱数据
 * @param {string} courseId 知识图谱ID
 * @param {Object} data 知识图谱数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseGraph = (courseId, data) => {
  // 将前端格式转换为后端所需的数据结构
  const requestData = {
    graph: {
      id: data.graphId || courseId,
      name: data.name || '未命名图谱',
      content: data.content || ''
    },
    nodes: data.nodes.map(node => ({
      id: node.id && !node.id.includes('node') ? Number(node.id) : null, // 如果是新节点(id格式如'node1')则不发送id
      graph_id: data.graphId || courseId,
      parent_id: node.parentId ? Number(node.parentId) : null,
      name: node.text || '',
      content: node.content || ''
    })),
    lines: data.links.map(link => ({
      id: link.id && !link.id.includes('line') ? Number(link.id) : null,
      node_id: Number(link.from),
      target_id: Number(link.to),
      content: link.text || ''
    })),
    nodeStyles: data.nodes.map(node => ({
      node_id: Number(node.id),
      type: node.type || '',
      color: node.color || '',
      fontColor: node.fontColor || '',
      node_shape: node.nodeShape || 0,
      width: node.width || null,
      height: node.height || null,
      border_width: node.borderWidth || null,
      border_height: node.borderHeight || null,
      fixed: node.fixed || '',
      x: node.x || null,
      y: node.y || null
    }))
  };

  return request({
    url: `/api/graph/${courseId}`,
    method: 'post',
    data: requestData
  }).catch(error => {
    console.error(`保存知识图谱 ${courseId} 数据失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存知识图谱大纲
 * @param {string} courseId 知识图谱ID
 * @param {Object} data 大纲数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseOutline = (courseId, data) => {
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'post',
    data
  }).catch(error => {
    console.error(`保存知识图谱 ${courseId} 大纲失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 从大纲创建知识图谱
 * @param {string} courseId 知识图谱ID
 * @returns {Promise<Object>} 知识图谱数据
 */
export const createGraphFromOutline = (courseId) => {
  console.log(`从大纲创建知识图谱: ${courseId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-to-graph`,
    method: 'post'
  }).catch(error => {
    console.error(`从大纲创建知识图谱失败: ${courseId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 从知识图谱更新大纲
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 更新结果
 */
export const updateOutlineFromGraph = (graphId) => {
  console.log(`从知识图谱更新大纲: ${graphId}`);
  return request({
    url: `/api/graph/graphs/${graphId}/graph-to-outline`,
    method: 'post'
  }).catch(error => {
    console.error(`从知识图谱更新大纲失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 同步节点映射关系
 * @param {string} courseId 知识图谱ID
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 同步结果
 */
export const syncNodeMappings = (courseId, graphId) => {
  console.log(`同步节点映射关系: 知识图谱${courseId}, 图谱${graphId}`);
  return request({
    url: `/api/graph/courses/${courseId}/graphs/${graphId}/sync-mappings`,
    method: 'post'
  }).catch(error => {
    console.error(`同步节点映射关系失败: 知识图谱${courseId}, 图谱${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取大纲节点关联的知识点
 * @param {string} courseId 知识图谱ID
 * @param {string} nodeId 大纲节点ID
 * @returns {Promise<Array>} 关联的知识点列表
 */
export const getKnowledgeNodesForOutlineNode = (courseId, nodeId) => {
  console.log(`获取大纲节点关联的知识点: 知识图谱${courseId}, 节点${nodeId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-nodes/${nodeId}/knowledge-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取大纲节点关联的知识点失败: 知识图谱${courseId}, 节点${nodeId}`, error);
    return [];
  });
};

/**
 * 获取知识点关联的大纲节点
 * @param {string} nodeId 知识点ID
 * @returns {Promise<Array>} 关联的大纲节点列表
 */
export const getOutlineNodesForKnowledgeNode = (nodeId) => {
  console.log(`获取知识点关联的大纲节点: 知识点${nodeId}`);
  return request({
    url: `/api/graph/knowledge-nodes/${nodeId}/outline-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取知识点关联的大纲节点失败: 知识点${nodeId}`, error);
    return [];
  });
};

/**
 * 搜索知识图谱列表（带查询条件）
 * @param {Object} searchParams 搜索参数
 * @param {string} searchParams.name 图谱名字（模糊搜索）
 * @param {string} searchParams.content 图谱内容文本（模糊搜索）
 * @param {number} searchParams.courseId 课程id（精确匹配）
 * @param {string} searchParams.graphType 图谱类型，0为图谱,1为章节（精确匹配）
 * @param {number} searchParams.pageNum 页码，默认1
 * @param {number} searchParams.pageSize 每页大小，默认100
 * @returns {Promise<Object>} 图谱列表响应数据，格式为TableDataInfo
 */
export const searchGraphList = (searchParams = {}) => {
  console.log('搜索知识图谱列表...', searchParams);

  // 构建zstpGraph查询对象
  const zstpGraph = {};

  // 添加搜索条件
  if (searchParams.name) {
    zstpGraph.name = searchParams.name;
  }
  if (searchParams.content) {
    zstpGraph.content = searchParams.content;
  }
  if (searchParams.courseId) {
    zstpGraph.courseId = searchParams.courseId;
  }
  if (searchParams.graphType !== undefined && searchParams.graphType !== null) {
    zstpGraph.graphType = searchParams.graphType;
  }

  // 调用基础的getGraphList函数
  return getGraphList({
    zstpGraph: zstpGraph,
    pageNum: searchParams.pageNum || 1,
    pageSize: searchParams.pageSize || 100
  });
};

// 知识图谱相关的函数别名 - 更明确的命名
export const getKnowledgeGraphList = getGraphList;
export const getKnowledgeGraphNodes = getNodeList;
export const createKnowledgeGraph = createGraph;
export const updateKnowledgeGraph = updateGraph;
export const deleteKnowledgeGraph = deleteGraph;